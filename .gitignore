# Django
*.log
*.pot
*.pyc
__pycache__/
local_settings.py
db.sqlite3
db.sqlite3-journal

# Environment variables
.env

# Virtual environment
venv/
.venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Media files (uploaded by users)
media/
static/profile_pic/CustomerProfilePic/

# Face Recognition data
ecom/FaceRecognition/dataset/*/
ecom/FaceRecognition/encodings/*.pkl
ecom/FaceRecognition/encodings/*.json

# Python
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Testing
.coverage
.pytest_cache/
htmlcov/

# Documentation
docs/_build/

# Backup files
*.bak
*.backup
*.orig

# Temporary files
*.tmp
*.temp

# Node.js (if using npm for frontend)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Static files collected for production
staticfiles/
static_root/
