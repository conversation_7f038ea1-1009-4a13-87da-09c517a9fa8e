# Django Configuration
SECRET_KEY=your-secret-key-here
DEBUG=True

# Email Configuration (for contact form)
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
EMAIL_RECEIVING_USER=<EMAIL>

# PayPal Configuration
PAYPAL_RECEIVER_EMAIL=<EMAIL>

# Database Configuration (if using external database)
# DATABASE_URL=postgresql://user:password@localhost:5432/dbname

# Stripe Configuration (if using Stripe)
# STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
# STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key

# PhonePe Configuration (if using PhonePe)
# PHONEPE_MERCHANT_ID=your_merchant_id
# PHONEPE_SALT_KEY=your_salt_key

# Google Pay Configuration (if using Google Pay)
# GOOGLE_PAY_MERCHANT_ID=your_google_pay_merchant_id
